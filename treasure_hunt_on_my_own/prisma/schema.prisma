// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Hunt {
  id        Int      @id @default(autoincrement())
  title     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  treasures Treasure[]
}

model Treasure {
  id        Int      @id @default(autoincrement())
  huntId    Int
  hunt      Hunt     @relation(fields: [huntId], references: [id], onDelete: Cascade)
  ordinal   Int
  qrCodeData String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  clue      Clue?
}

model Clue {
  id        Int      @id @default(autoincrement())
  treasureId Int
  treasure  Treasure @relation(fields: [treasureId], references: [id], onDelete: Cascade)
  text      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
