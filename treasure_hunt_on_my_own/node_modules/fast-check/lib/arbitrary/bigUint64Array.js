"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bigUint64Array = bigUint64Array;
const globals_1 = require("../utils/globals");
const bigInt_1 = require("./bigInt");
const TypedIntArrayArbitraryBuilder_1 = require("./_internals/builders/TypedIntArrayArbitraryBuilder");
function bigUint64Array(constraints = {}) {
    return (0, TypedIntArrayArbitraryBuilder_1.typedIntArrayArbitraryArbitraryBuilder)(constraints, (0, globals_1.BigInt)(0), (0, globals_1.BigInt)('18446744073709551615'), globals_1.BigUint64Array, bigInt_1.bigInt);
}
